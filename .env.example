# Application Settings
APP_NAME=MagicGateway
APP_VERSION=0.1.0
DEBUG=False
ENVIRONMENT=development  # development, staging, production
LOG_LEVEL=INFO

# Server Settings
HOST=0.0.0.0
PORT=8000

# Security Settings
SECRET_KEY=your-secret-key-here  # Generate a secure key for production
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# LDAP Settings
LDAP_SERVER_URL=ldap://your-ldap-server:389
LDAP_BIND_DN=cn=admin,dc=example,dc=com
LDAP_BIND_PASSWORD=admin_password
LDAP_USER_SEARCH_BASE=ou=users,dc=example,dc=com
LDAP_USER_SEARCH_FILTER=(uid={username})
LDAP_ADMIN_GROUP_DN=cn=admins,ou=groups,dc=example,dc=com

# PostgreSQL Settings
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=magic_gateway
POSTGRES_MIN_CONNECTIONS=1
POSTGRES_MAX_CONNECTIONS=10
POSTGRES_POOL_TIMEOUT=30  # Timeout for acquiring a connection from the pool
POSTGRES_MAX_IDLE_TIME=600  # Maximum time a connection can be idle
POSTGRES_MAX_LIFETIME=3600  # Maximum lifetime of a connection

# Logs Database Settings (separate PostgreSQL connection for logs)
LOGS_DB_HOST=***********
LOGS_DB_PORT=5432
LOGS_DB_USER=msr.shinyproxy.svc
LOGS_DB_PASSWORD=63wClvYSbfyCFH
LOGS_DB_NAME=logs
LOGS_DB_SCHEMA=api

# ClickHouse Settings
CLICKHOUSE_HOST=***********
CLICKHOUSE_PORT=9000  # Native client port
CLICKHOUSE_HTTP_PORT=8123  # HTTP interface port
CLICKHOUSE_USER=msr.shinyproxy.svc
CLICKHOUSE_PASSWORD=63wClvYSbfyCFH
CLICKHOUSE_DATABASE=default
CLICKHOUSE_MIN_CONNECTIONS=1
CLICKHOUSE_MAX_CONNECTIONS=10
CLICKHOUSE_CONNECT_TIMEOUT=10  # Connection timeout in seconds
CLICKHOUSE_PING_TIMEOUT=5  # Timeout for liveness check in seconds
CLICKHOUSE_POOL_WAIT_TIMEOUT=30  # Timeout for waiting for a connection from the pool
CLICKHOUSE_SOCKET_TIMEOUT=30  # Socket timeout for send/receive operations

# Query Settings
MAX_QUERY_EXECUTION_TIME=300  # seconds
TRACK_QUERIES=True
