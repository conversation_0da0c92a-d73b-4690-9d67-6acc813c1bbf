"""
Parquet file processing utilities for the MagicGateway application.

This module provides functions to read and process Parquet files created by
ClickHouse's native export functionality.
"""

import os
import tempfile
import gc
import time
import psutil
from typing import Any, Dict, List, Optional, AsyncGenerator, Tuple
import asyncio
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor

import pyarrow as pa
import pyarrow.parquet as pq
from fastapi.responses import StreamingResponse, FileResponse
from starlette.background import BackgroundTask

from magic_gateway.core.logging_config import log
from magic_gateway.utils.streaming_excel_writer import identify_period_column

# Constants for streaming optimization
DEFAULT_STREAMING_CHUNK_SIZE = 500000  # 500K rows per chunk for streaming
MEMORY_THRESHOLD_MB = 3000  # 3GB memory threshold
MIN_CHUNK_SIZE = 50000  # Minimum chunk size
MAX_CHUNK_SIZE = 1000000  # Maximum chunk size

# Progress tracking constants
PROGRESS_LOG_INTERVAL = 100000  # Log progress every 100K rows
MEMORY_CHECK_INTERVAL = 5  # Check memory every 5 batches


def get_memory_usage_mb() -> float:
    """Get current memory usage in MB."""
    try:
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    except Exception:
        return 0.0


def calculate_adaptive_chunk_size(
    file_size_bytes: int, available_memory_mb: float, num_columns: Optional[int] = None
) -> int:
    """
    Calculate adaptive chunk size based on file size, available memory, and dataset characteristics.

    Args:
        file_size_bytes: Size of the Parquet file in bytes
        available_memory_mb: Available memory in MB
        num_columns: Number of columns in the dataset (for better estimation)

    Returns:
        Optimal chunk size for processing
    """
    # More accurate row estimation based on columns
    if num_columns:
        # Estimate bytes per row based on column count (more columns = more bytes per row)
        estimated_bytes_per_row = max(
            50, min(500, num_columns * 8)
        )  # 8 bytes per column average
    else:
        estimated_bytes_per_row = 100  # Default estimate

    estimated_rows = file_size_bytes // estimated_bytes_per_row

    # Calculate chunk size based on available memory
    # Use 5-15% of available memory per chunk depending on file size
    if file_size_bytes > 1024 * 1024 * 1024:  # > 1GB
        memory_percentage = 0.05  # Use 5% for very large files
    elif file_size_bytes > 100 * 1024 * 1024:  # > 100MB
        memory_percentage = 0.10  # Use 10% for large files
    else:
        memory_percentage = 0.15  # Use 15% for smaller files

    memory_based_chunk = int(
        (available_memory_mb * memory_percentage * 1024 * 1024)
        // estimated_bytes_per_row
    )

    # Use the smaller of memory-based or default chunk size
    chunk_size = min(memory_based_chunk, DEFAULT_STREAMING_CHUNK_SIZE)

    # Ensure chunk size is within bounds
    chunk_size = max(MIN_CHUNK_SIZE, min(MAX_CHUNK_SIZE, chunk_size))

    log.info(
        f"Calculated adaptive chunk size: {chunk_size} rows "
        f"(file: {file_size_bytes} bytes, memory: {available_memory_mb:.1f} MB, "
        f"cols: {num_columns}, est_bytes_per_row: {estimated_bytes_per_row})"
    )
    return chunk_size


class StreamingProgressTracker:
    """
    Progress tracker for streaming operations that don't know total size upfront.
    """

    def __init__(self, operation_name: str, total_rows: Optional[int] = None):
        self.operation_name = operation_name
        self.total_rows = total_rows
        self.processed_rows = 0
        self.start_time = time.time()
        self.last_log_time = self.start_time
        self.last_log_rows = 0

    def update(self, rows_processed: int, memory_usage_mb: Optional[float] = None):
        """Update progress and log if needed."""
        self.processed_rows += rows_processed
        current_time = time.time()

        # Log progress every PROGRESS_LOG_INTERVAL rows or every 30 seconds
        should_log = (
            self.processed_rows % PROGRESS_LOG_INTERVAL == 0
            or (current_time - self.last_log_time) >= 30
        )

        if should_log:
            elapsed = current_time - self.start_time
            rows_per_sec = self.processed_rows / elapsed if elapsed > 0 else 0

            # Calculate recent rate
            recent_elapsed = current_time - self.last_log_time
            recent_rows = self.processed_rows - self.last_log_rows
            recent_rate = recent_rows / recent_elapsed if recent_elapsed > 0 else 0

            progress_msg = (
                f"{self.operation_name}: {self.processed_rows:,} rows processed"
            )

            if self.total_rows:
                percentage = (self.processed_rows / self.total_rows) * 100
                progress_msg += f" ({percentage:.1f}%)"

            progress_msg += (
                f" | Rate: {rows_per_sec:.0f} rows/sec (recent: {recent_rate:.0f})"
            )

            if memory_usage_mb:
                progress_msg += f" | Memory: {memory_usage_mb:.1f} MB"

            log.info(progress_msg)

            self.last_log_time = current_time
            self.last_log_rows = self.processed_rows

    def complete(self):
        """Log completion statistics."""
        elapsed = time.time() - self.start_time
        avg_rate = self.processed_rows / elapsed if elapsed > 0 else 0
        log.info(
            f"{self.operation_name}: Completed - {self.processed_rows:,} rows in {elapsed:.2f}s "
            f"(avg: {avg_rate:.0f} rows/sec)"
        )


def monitor_memory_and_adjust_chunk_size(
    current_chunk_size: int,
    memory_usage_mb: float,
    target_memory_mb: float = MEMORY_THRESHOLD_MB,
) -> int:
    """
    Monitor memory usage and adjust chunk size dynamically.

    Args:
        current_chunk_size: Current chunk size being used
        memory_usage_mb: Current memory usage in MB
        target_memory_mb: Target memory threshold in MB

    Returns:
        Adjusted chunk size
    """
    memory_usage_ratio = memory_usage_mb / target_memory_mb

    if memory_usage_ratio > 0.9:  # Using > 90% of target memory
        # Reduce chunk size aggressively
        new_chunk_size = int(current_chunk_size * 0.5)
        log.warning(
            f"High memory usage ({memory_usage_mb:.1f} MB), reducing chunk size from {current_chunk_size} to {new_chunk_size}"
        )
    elif memory_usage_ratio > 0.7:  # Using > 70% of target memory
        # Reduce chunk size moderately
        new_chunk_size = int(current_chunk_size * 0.75)
        log.info(
            f"Moderate memory usage ({memory_usage_mb:.1f} MB), reducing chunk size from {current_chunk_size} to {new_chunk_size}"
        )
    elif memory_usage_ratio < 0.3:  # Using < 30% of target memory
        # Increase chunk size for better performance
        new_chunk_size = int(current_chunk_size * 1.5)
        log.info(
            f"Low memory usage ({memory_usage_mb:.1f} MB), increasing chunk size from {current_chunk_size} to {new_chunk_size}"
        )
    else:
        # Memory usage is optimal, keep current chunk size
        new_chunk_size = current_chunk_size

    # Ensure new chunk size is within bounds
    new_chunk_size = max(MIN_CHUNK_SIZE, min(MAX_CHUNK_SIZE, new_chunk_size))

    return new_chunk_size


def read_parquet_file_streaming(
    file_path: str, chunk_size: Optional[int] = None
) -> AsyncGenerator[List[Dict[str, Any]], None]:
    """
    True streaming Parquet reader that processes data in batches without loading entire file.

    This function uses PyArrow's batch reading capabilities to process large Parquet files
    with constant memory usage, regardless of file size.

    Args:
        file_path: Path to the Parquet file
        chunk_size: Number of rows per chunk (auto-calculated if None)

    Yields:
        Chunks of data as lists of dictionaries

    Raises:
        Exception: If the file cannot be read
    """
    try:
        log.info(f"Starting streaming Parquet read: {file_path}")

        # Get file info for adaptive chunk sizing
        file_size = os.path.getsize(file_path)
        current_memory = get_memory_usage_mb()
        available_memory = MEMORY_THRESHOLD_MB - current_memory

        # Open Parquet file to get metadata
        parquet_file = pq.ParquetFile(file_path)
        total_rows = parquet_file.metadata.num_rows
        num_columns = parquet_file.metadata.num_columns

        if chunk_size is None:
            chunk_size = calculate_adaptive_chunk_size(
                file_size, available_memory, num_columns
            )

        log.info(
            f"Parquet file contains {total_rows} rows, {num_columns} columns, using initial chunk size: {chunk_size}"
        )

        # Initialize progress tracker
        progress_tracker = StreamingProgressTracker(
            f"Streaming Parquet read ({os.path.basename(file_path)})", total_rows
        )

        # Read in batches using PyArrow's batch iterator with adaptive chunk sizing
        current_chunk_size = chunk_size
        batch_count = 0

        for batch in parquet_file.iter_batches(batch_size=current_chunk_size):
            # Convert batch to list of dictionaries
            chunk_data = batch.to_pylist()
            chunk_size_actual = len(chunk_data)
            batch_count += 1

            # Monitor memory usage and adjust chunk size every MEMORY_CHECK_INTERVAL batches
            if batch_count % MEMORY_CHECK_INTERVAL == 0:
                memory_usage = get_memory_usage_mb()
                new_chunk_size = monitor_memory_and_adjust_chunk_size(
                    current_chunk_size, memory_usage
                )

                if new_chunk_size != current_chunk_size:
                    current_chunk_size = new_chunk_size
                    log.info(f"Adjusted chunk size to {current_chunk_size} rows")

            # Update progress tracker
            memory_usage = get_memory_usage_mb()
            progress_tracker.update(chunk_size_actual, memory_usage)

            yield chunk_data

            # Force garbage collection to keep memory usage low
            if batch_count % 10 == 0:  # Every 10 batches
                gc.collect()

        progress_tracker.complete()

    except Exception as e:
        log.error(f"Error in streaming Parquet read {file_path}: {e}", exc_info=True)
        raise


def read_parquet_file_in_chunks(
    file_path: str, chunk_size: int = 100000
) -> List[List[Dict[str, Any]]]:
    """
    Legacy function: Read a Parquet file in chunks and return as list of dictionaries.

    WARNING: This function loads the entire file into memory and should be avoided for large files.
    Use read_parquet_file_streaming() for memory-efficient processing.

    Args:
        file_path: Path to the Parquet file
        chunk_size: Number of rows per chunk

    Returns:
        List of chunks, where each chunk is a list of dictionaries

    Raises:
        Exception: If the file cannot be read
    """
    try:
        log.warning(
            f"Using legacy read_parquet_file_in_chunks for {file_path} - consider using streaming version"
        )

        # Read the Parquet file
        table = pq.read_table(file_path)
        total_rows = len(table)

        log.info(f"Parquet file contains {total_rows} rows")

        # Convert to list of dictionaries in chunks
        chunks = []
        for start_idx in range(0, total_rows, chunk_size):
            end_idx = min(start_idx + chunk_size, total_rows)
            chunk_table = table.slice(start_idx, end_idx - start_idx)
            chunk_dict = chunk_table.to_pylist()
            chunks.append(chunk_dict)

        log.info(f"Split Parquet data into {len(chunks)} chunks")
        return chunks

    except Exception as e:
        log.error(f"Error reading Parquet file {file_path}: {e}", exc_info=True)
        raise


async def read_parquet_file_async(
    file_path: str, chunk_size: int = 100000
) -> AsyncGenerator[List[Dict[str, Any]], None]:
    """
    Asynchronously read a Parquet file in chunks.

    Args:
        file_path: Path to the Parquet file
        chunk_size: Number of rows per chunk

    Yields:
        Chunks of data as lists of dictionaries
    """
    try:
        log.info(f"Starting async Parquet file read: {file_path}")

        # Use thread pool to avoid blocking the event loop
        with ThreadPoolExecutor() as executor:
            loop = asyncio.get_event_loop()
            chunks = await loop.run_in_executor(
                executor, read_parquet_file_in_chunks, file_path, chunk_size
            )

        # Yield chunks asynchronously
        for chunk in chunks:
            yield chunk
            # Allow other tasks to run
            await asyncio.sleep(0)

    except Exception as e:
        log.error(f"Error in async Parquet file read: {e}", exc_info=True)
        # Yield empty chunk on error
        yield []


async def read_parquet_file_async_streaming(
    file_path: str, chunk_size: Optional[int] = None
) -> AsyncGenerator[List[Dict[str, Any]], None]:
    """
    Memory-efficient async Parquet reader using true streaming approach.

    This function provides memory-efficient streaming of large Parquet files
    without loading the entire dataset into memory.

    Args:
        file_path: Path to the Parquet file
        chunk_size: Number of rows per chunk (auto-calculated if None)

    Yields:
        Chunks of data as lists of dictionaries
    """
    try:
        log.info(f"Starting memory-efficient async Parquet read: {file_path}")

        # Use thread pool to avoid blocking the event loop while reading batches
        with ThreadPoolExecutor(max_workers=1) as executor:
            loop = asyncio.get_event_loop()

            # Get file info for adaptive chunk sizing
            file_size = os.path.getsize(file_path)
            current_memory = get_memory_usage_mb()
            available_memory = MEMORY_THRESHOLD_MB - current_memory

            # Open Parquet file for batch reading in thread pool
            def open_parquet_file():
                return pq.ParquetFile(file_path)

            parquet_file = await loop.run_in_executor(executor, open_parquet_file)
            total_rows = parquet_file.metadata.num_rows
            num_columns = parquet_file.metadata.num_columns

            if chunk_size is None:
                chunk_size = calculate_adaptive_chunk_size(
                    file_size, available_memory, num_columns
                )

            log.info(
                f"Parquet file contains {total_rows} rows, {num_columns} columns, using initial chunk size: {chunk_size}"
            )

            # Initialize progress tracker
            progress_tracker = StreamingProgressTracker(
                f"Async streaming Parquet read ({os.path.basename(file_path)})",
                total_rows,
            )

            # Process batches one at a time to maintain low memory usage with adaptive sizing
            current_chunk_size = chunk_size
            batch_count = 0

            def read_next_batch(batch_iter, batch_size):
                """Read next batch from iterator."""
                try:
                    batch = next(batch_iter)
                    return batch.to_pylist()
                except StopIteration:
                    return None

            # Create batch iterator in thread pool
            def create_batch_iterator():
                return parquet_file.iter_batches(batch_size=current_chunk_size)

            batch_iter = await loop.run_in_executor(executor, create_batch_iterator)

            while True:
                # Read next batch in thread pool to avoid blocking
                chunk_data = await loop.run_in_executor(
                    executor, read_next_batch, batch_iter, current_chunk_size
                )

                if chunk_data is None:
                    break

                chunk_size_actual = len(chunk_data)
                batch_count += 1

                # Monitor memory usage and adjust chunk size every MEMORY_CHECK_INTERVAL batches
                if batch_count % MEMORY_CHECK_INTERVAL == 0:
                    memory_usage = get_memory_usage_mb()
                    new_chunk_size = monitor_memory_and_adjust_chunk_size(
                        current_chunk_size, memory_usage
                    )

                    if new_chunk_size != current_chunk_size:
                        current_chunk_size = new_chunk_size
                        log.info(
                            f"Adjusted async chunk size to {current_chunk_size} rows"
                        )
                        # Note: We can't change the batch iterator mid-stream, but this will affect future processing

                # Update progress tracker
                memory_usage = get_memory_usage_mb()
                progress_tracker.update(chunk_size_actual, memory_usage)

                yield chunk_data

                # Allow other tasks to run and force garbage collection periodically
                await asyncio.sleep(0)
                if batch_count % 10 == 0:  # Every 10 batches
                    gc.collect()

            progress_tracker.complete()

    except Exception as e:
        log.error(f"Error in async streaming Parquet read: {e}", exc_info=True)
        raise


def create_parquet_file_response(
    file_path: str, filename: str, cleanup_file: bool = True
) -> FileResponse:
    """
    Create a FileResponse for a Parquet file with optional cleanup.

    Args:
        file_path: Path to the Parquet file
        filename: Name for the downloaded file
        cleanup_file: Whether to delete the file after sending

    Returns:
        FileResponse for the Parquet file
    """
    # Create background task to clean up file if requested
    background_task = None
    if cleanup_file:

        def cleanup():
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    log.info(f"Cleaned up temporary Parquet file: {file_path}")
            except Exception as e:
                log.warning(f"Failed to clean up Parquet file {file_path}: {e}")

        background_task = BackgroundTask(cleanup)

    return FileResponse(
        path=file_path,
        filename=f"{filename}.parquet",
        media_type="application/octet-stream",
        background=background_task,
    )


async def convert_parquet_to_csv_stream(
    file_path: str, chunk_size: int = 100000
) -> AsyncGenerator[str, None]:
    """
    Convert a Parquet file to CSV format as a streaming response.

    Args:
        file_path: Path to the Parquet file
        chunk_size: Number of rows per chunk

    Yields:
        CSV data as strings
    """
    import csv
    import io

    try:
        log.info(f"Converting Parquet to CSV stream: {file_path}")

        # Read the first chunk to get headers
        first_chunk_yielded = False

        async for chunk in read_parquet_file_async(file_path, chunk_size):
            if not chunk:
                continue

            # Create CSV string for this chunk
            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=chunk[0].keys())

            # Write header only for the first chunk
            if not first_chunk_yielded:
                writer.writeheader()
                first_chunk_yielded = True

            # Write rows
            writer.writerows(chunk)

            # Yield the CSV data
            csv_data = output.getvalue()
            yield csv_data

            # Allow other tasks to run
            await asyncio.sleep(0)

    except Exception as e:
        log.error(f"Error converting Parquet to CSV: {e}", exc_info=True)
        # Yield empty string on error
        yield ""


def get_parquet_file_info(file_path: str) -> Dict[str, Any]:
    """
    Get information about a Parquet file.

    Args:
        file_path: Path to the Parquet file

    Returns:
        Dictionary with file information
    """
    try:
        # Read Parquet file metadata
        parquet_file = pq.ParquetFile(file_path)

        # Get basic info
        info = {
            "file_path": file_path,
            "file_size_bytes": os.path.getsize(file_path),
            "num_rows": parquet_file.metadata.num_rows,
            "num_columns": parquet_file.metadata.num_columns,
            "num_row_groups": parquet_file.metadata.num_row_groups,
            "schema": str(parquet_file.schema_arrow),
            "column_names": parquet_file.schema_arrow.names,
        }

        return info

    except Exception as e:
        log.error(f"Error getting Parquet file info: {e}", exc_info=True)
        return {"file_path": file_path, "error": str(e)}


def cleanup_parquet_file(file_path: str) -> None:
    """
    Clean up a temporary Parquet file.

    Args:
        file_path: Path to the file to clean up
    """
    try:
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
            log.info(f"Cleaned up Parquet file: {file_path}")
        elif file_path:
            log.debug(f"Parquet file does not exist for cleanup: {file_path}")
    except Exception as e:
        log.warning(f"Failed to clean up Parquet file {file_path}: {e}")


def safe_cleanup_parquet_files(*file_paths: str) -> None:
    """
    Safely clean up multiple Parquet files, continuing even if some fail.

    Args:
        *file_paths: Variable number of file paths to clean up
    """
    for file_path in file_paths:
        if file_path:
            cleanup_parquet_file(file_path)


async def group_parquet_data_by_periods_streaming(
    file_path: str, chunk_size: Optional[int] = None
) -> Dict[str, List[List[Dict[str, Any]]]]:
    """
    Memory-efficient grouping of Parquet file data by period values for separate Excel sheets.

    Uses streaming approach to minimize memory usage while grouping data.

    Args:
        file_path: Path to the Parquet file
        chunk_size: Number of rows per chunk when reading (auto-calculated if None)

    Returns:
        Dictionary mapping period values to lists of data chunks

    Raises:
        Exception: If the file cannot be read or processed
    """
    try:
        log.info(f"Streaming grouping of Parquet data by periods: {file_path}")

        # Dictionary to store data grouped by periods
        period_groups = {}
        period_column = None
        total_rows_processed = 0

        # Read data in chunks and group by periods using streaming approach
        async for chunk in read_parquet_file_async_streaming(file_path, chunk_size):
            if not chunk:
                continue

            # Identify period column from first chunk
            if period_column is None:
                headers = list(chunk[0].keys()) if chunk else []
                period_column = identify_period_column(headers)

                if not period_column:
                    log.warning(
                        "No period column identified, using 'All Data' as single period"
                    )
                    period_column = None
                else:
                    log.info(f"Identified period column: {period_column}")

            # Group rows by period value
            period_chunk_groups = {}
            for row in chunk:
                period_value = row.get(period_column) if period_column else "All Data"
                period_key = (
                    str(period_value) if period_value is not None else "All Data"
                )

                if period_key not in period_chunk_groups:
                    period_chunk_groups[period_key] = []
                period_chunk_groups[period_key].append(row)

            # Add grouped chunks to main period groups
            for period_key, period_rows in period_chunk_groups.items():
                if period_key not in period_groups:
                    period_groups[period_key] = []
                period_groups[period_key].append(period_rows)

            total_rows_processed += len(chunk)

            # Log progress and force garbage collection periodically
            if total_rows_processed % 500000 == 0:
                memory_usage = get_memory_usage_mb()
                log.info(
                    f"Grouped {total_rows_processed} rows by periods, memory: {memory_usage:.1f} MB"
                )
                gc.collect()

        log.info(
            f"Completed period grouping: {len(period_groups)} periods, {total_rows_processed} total rows"
        )
        return period_groups

    except Exception as e:
        log.error(f"Error grouping Parquet data by periods: {e}", exc_info=True)
        raise


async def group_parquet_data_by_periods(
    file_path: str, chunk_size: int = 100000
) -> Dict[str, List[List[Dict[str, Any]]]]:
    """
    Legacy function: Group Parquet file data by period values for separate Excel sheets.

    WARNING: This function may use significant memory for large files.
    Use group_parquet_data_by_periods_streaming() for memory-efficient processing.

    Args:
        file_path: Path to the Parquet file
        chunk_size: Number of rows per chunk when reading

    Returns:
        Dictionary mapping period values to lists of data chunks

    Raises:
        Exception: If the file cannot be read or processed
    """
    try:
        log.warning(
            f"Using legacy group_parquet_data_by_periods for {file_path} - consider using streaming version"
        )

        # Dictionary to store data grouped by periods
        period_groups = {}
        period_column = None

        # Read data in chunks and group by periods
        async for chunk in read_parquet_file_async(file_path, chunk_size):
            if not chunk:
                continue

            # Identify period column from first chunk
            if period_column is None:
                headers = list(chunk[0].keys()) if chunk else []
                period_column = identify_period_column(headers)

                if not period_column:
                    log.warning(
                        "No period column identified, using 'All Data' as single period"
                    )
                    period_column = None
                else:
                    log.info(f"Identified period column: {period_column}")

            # Group chunk data by period values
            if period_column:
                # Group by actual period values
                chunk_groups = {}
                for row in chunk:
                    period_value = str(row.get(period_column, "Unknown"))
                    if period_value not in chunk_groups:
                        chunk_groups[period_value] = []
                    chunk_groups[period_value].append(row)

                # Add to main period groups
                for period_value, period_rows in chunk_groups.items():
                    if period_value not in period_groups:
                        period_groups[period_value] = []
                    period_groups[period_value].append(period_rows)
            else:
                # No period column found, put all data in single group
                if "All Data" not in period_groups:
                    period_groups["All Data"] = []
                period_groups["All Data"].append(chunk)

        # Log summary
        total_periods = len(period_groups)
        total_chunks = sum(len(chunks) for chunks in period_groups.values())
        log.info(
            f"Grouped data into {total_periods} periods with {total_chunks} total chunks"
        )

        for period, chunks in period_groups.items():
            total_rows = sum(len(chunk) for chunk in chunks)
            log.debug(f"Period '{period}': {len(chunks)} chunks, {total_rows} rows")

        return period_groups

    except Exception as e:
        log.error(f"Error grouping Parquet data by periods: {e}", exc_info=True)
        raise
