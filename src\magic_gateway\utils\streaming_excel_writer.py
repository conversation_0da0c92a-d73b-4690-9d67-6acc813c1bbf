"""True streaming Excel writer using xlsxwriter with constant memory optimization."""

import logging
import tempfile
import os
import time
import gc
import math
import psutil
import platform
from typing import (
    List,
    Dict,
    Any,
    Optional,
    Tuple,
    Callable,
)

import xlsxwriter
from fastapi.responses import StreamingResponse
from starlette.background import BackgroundTask
import xlsxwriter.worksheet
from magic_gateway.utils.info_sheet_generator import InfoSheetGenerator

# Configure logging
log = logging.getLogger(__name__)

# Windows-specific constants
IS_WINDOWS = platform.system() == "Windows"
WINDOWS_FILE_RETRY_ATTEMPTS = 3
WINDOWS_FILE_RETRY_DELAY = 0.5  # seconds


def clean_excel_value(value: Any) -> Any:
    """
    Clean a value for Excel export by handling NaN, INF, NULL, and NA values.

    Args:
        value: The value to clean

    Returns:
        Cleaned value safe for Excel export
    """
    if value is None:
        return ""

    # Handle numeric NaN and INF values
    if isinstance(value, (int, float)):
        if math.isnan(value) or math.isinf(value):
            return ""

    # Handle string representations of NULL/NA/NaN
    str_value = str(value).strip().lower()
    if str_value in ("null", "na", "nan", "none", ""):
        return ""

    return value


# Constants for optimization
OPTIMAL_CHUNK_SIZE = 256 * 1024  # 256KB chunks for file streaming
PROGRESS_LOG_INTERVAL = 100000  # Log progress every 100,000 rows
MAX_ROWS_PER_SHEET = 900000  # Split sheets at 900K rows
MEDIUM_DATASET_THRESHOLD = 250000  # Use temp files above 250K rows


def safe_remove_file(file_path: str) -> bool:
    """
    Safely remove a file with Windows-specific retry logic for file handle issues.

    Args:
        file_path: Path to the file to remove

    Returns:
        True if file was successfully removed, False otherwise
    """
    if not os.path.exists(file_path):
        return True

    for attempt in range(WINDOWS_FILE_RETRY_ATTEMPTS):
        try:
            os.unlink(file_path)
            log.debug(f"Successfully removed file: {file_path}")
            return True
        except OSError as e:
            if IS_WINDOWS and e.errno == 32:  # WinError 32: file in use
                if attempt < WINDOWS_FILE_RETRY_ATTEMPTS - 1:
                    log.debug(
                        f"File in use (attempt {attempt + 1}/{WINDOWS_FILE_RETRY_ATTEMPTS}): {file_path}. "
                        f"Retrying in {WINDOWS_FILE_RETRY_DELAY}s..."
                    )
                    time.sleep(WINDOWS_FILE_RETRY_DELAY)
                    continue
                else:
                    log.warning(
                        f"Failed to remove file after {WINDOWS_FILE_RETRY_ATTEMPTS} attempts "
                        f"(WinError 32 - file in use): {file_path}"
                    )
                    return False
            else:
                log.error(f"Error removing file {file_path}: {e}")
                return False
        except Exception as e:
            log.error(f"Unexpected error removing file {file_path}: {e}")
            return False

    return False


def get_memory_usage_mb() -> float:
    """Get current memory usage in MB."""
    try:
        process = psutil.Process()
        return process.memory_info().rss / 1024 / 1024
    except Exception:
        return 0.0


def identify_period_column(headers: List[str]) -> Optional[str]:
    """Identify the period column for sheet splitting."""
    period_keywords = ["period", "date", "time", "year", "month", "quarter"]
    for header in headers:
        header_lower = header.lower()
        if any(keyword in header_lower for keyword in period_keywords):
            return header
    return None


def identify_fact_value_columns(
    row_data: Dict[str, Any],
) -> Tuple[Optional[str], Optional[str]]:
    """Identify the fact and value columns in a row of data."""
    if not row_data:
        return None, None

    # Common names for fact columns
    fact_keywords = ["fact", "metric", "measure", "indicator", "kpi"]
    # Common names for value columns
    value_keywords = ["value", "val", "amount", "result"]

    fact_col = None
    value_col = None

    # First pass: look for exact matches
    for col in row_data.keys():
        col_lower = col.lower()
        if col_lower in fact_keywords and not fact_col:
            fact_col = col
        elif col_lower in value_keywords and not value_col:
            value_col = col

    # Second pass: look for partial matches if exact matches weren't found
    if not fact_col or not value_col:
        for col in row_data.keys():
            col_lower = col.lower()
            if not fact_col and any(keyword in col_lower for keyword in fact_keywords):
                fact_col = col
            elif not value_col and any(
                keyword in col_lower for keyword in value_keywords
            ):
                value_col = col

    return fact_col, value_col


def identify_index_columns(
    row_data: Dict[str, Any], fact_col: str, value_col: str
) -> List[str]:
    """Identify index columns that should be used to group facts."""
    if not row_data or not fact_col or not value_col:
        return []

    # All columns except fact and value columns are index columns
    index_cols = [
        col for col in row_data.keys() if col != fact_col and col != value_col
    ]
    log.debug(f"Identified {len(index_cols)} index columns: {', '.join(index_cols)}")
    return index_cols


class StreamingExcelWriter:
    """
    True streaming Excel writer using xlsxwriter with constant memory optimization.

    Features:
    1. Constant memory usage regardless of dataset size
    2. Automatic sheet splitting for large datasets
    3. Memory usage monitoring
    4. Support for horizontal facts format
    5. Temporary file management for medium datasets
    """

    def __init__(self, filename: str, horizontal_facts: bool = False):
        """Initialize the streaming Excel writer."""
        self.filename = filename
        self.horizontal_facts = horizontal_facts

        # Create temporary file
        self.temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".xlsx")
        self.temp_file_path = self.temp_file.name
        self.temp_file.close()

        # Initialize xlsxwriter with constant memory mode
        self.workbook = xlsxwriter.Workbook(
            self.temp_file_path,
            {
                "constant_memory": True,
                "tmpdir": tempfile.gettempdir(),
                "default_date_format": "yyyy-mm-dd",
                "remove_timezone": True,
                "nan_inf_to_errors": True,  # Convert NaN/INF to Excel errors instead of crashing
            },
        )

        # Create formats
        self.header_format = self.workbook.add_format(
            {
                "bold": True,
                "font_name": "Arial",
                "font_size": 10,
                "bg_color": "#D8E4BC",
                "border": 1,
            }
        )

        self.info_header_format = self.workbook.add_format(
            {
                "bold": True,
                "font_name": "Arial",
                "font_size": 11,
                "bg_color": "#D8E4BC",
                "border": 1,
            }
        )

        # Initialize sheets and tracking
        self.sheets = {}
        self.current_sheet = None
        self.current_sheet_name = "Data"
        self.sheet_count = 0
        self.current_sheet_rows = 0

        # State tracking
        self.headers = []
        self.headers_written = False
        self.total_rows = 0
        self.start_time = time.time()
        self.period_column = None

        # Memory and performance tracking
        self.initial_memory_mb = get_memory_usage_mb()
        self.temp_files = []

        # Info sheet data (to be added last)
        self.job_info = None

        # Horizontal facts support
        if horizontal_facts:
            self.fact_col = None
            self.value_col = None
            self.index_cols = []
            self.unique_facts = set()
            self.fact_values = {}
            self.temp_data_file = None

        log.info(
            f"Initialized StreamingExcelWriter for {filename} "
            f"(horizontal_facts={horizontal_facts}, memory={self.initial_memory_mb:.1f}MB)"
        )

    def _create_new_sheet(self, sheet_name: Optional[str] = None):
        """Create a new worksheet."""
        if sheet_name is None:
            self.sheet_count += 1
            if self.sheet_count == 1:
                sheet_name = "Data"
            else:
                sheet_name = f"Data_{self.sheet_count}"

        worksheet = self.workbook.add_worksheet(sheet_name)
        self.sheets[sheet_name] = worksheet
        self.current_sheet = worksheet
        self.current_sheet_name = sheet_name
        self.current_sheet_rows = 0

        # Write headers if we have them
        if self.headers:
            self._write_headers_to_sheet(worksheet)

        log.info(f"Created new sheet: {sheet_name}")
        return worksheet

    def _write_headers_to_sheet(self, worksheet):
        """Write headers to a specific worksheet."""
        for col, header in enumerate(self.headers):
            worksheet.write(0, col, header, self.header_format)
            worksheet.set_column(col, col, 15)  # Set column width
        self.current_sheet_rows = 1

    def _should_split_sheet(self, period_value: Optional[str] = None) -> bool:
        """Determine if we should split to a new sheet."""
        if self.current_sheet_rows >= MAX_ROWS_PER_SHEET:
            return True

        # For period-based splitting, check if period changed
        if period_value and self.period_column and self.sheet_count > 0:
            # This is a simplified check - in practice you'd want more sophisticated logic
            return False

        return False

    def write_data_chunk(self, chunk: List[Dict[str, Any]]):
        """Write a chunk of data with memory optimization."""
        if not chunk:
            return

        start_time = time.time()

        # Handle horizontal facts differently
        if self.horizontal_facts:
            self._process_horizontal_facts_chunk(chunk)
            return

        # Initialize headers from first chunk
        if not self.headers_written and chunk:
            self.headers = list(chunk[0].keys())
            self.period_column = identify_period_column(self.headers)
            self.headers_written = True

            # Create first sheet
            self._create_new_sheet()

        # Process each row
        for row_data in chunk:
            # Check if we need a new sheet
            period_value = (
                row_data.get(self.period_column) if self.period_column else None
            )
            if self._should_split_sheet(period_value):
                sheet_name = f"Data_{period_value}" if period_value else None
                self._create_new_sheet(sheet_name)

            # Write row to current sheet
            row_values = [
                clean_excel_value(row_data.get(key, "")) for key in self.headers
            ]
            for col, value in enumerate(row_values):
                self.current_sheet.write(self.current_sheet_rows, col, value)

            self.current_sheet_rows += 1
            self.total_rows += 1

        # Memory management
        if self.total_rows % 50000 == 0:
            gc.collect()
            current_memory = get_memory_usage_mb()
            memory_increase = current_memory - self.initial_memory_mb
            log.info(
                f"Processed {self.total_rows} rows, memory: {current_memory:.1f}MB (+{memory_increase:.1f}MB)"
            )

        # Log progress
        if self.total_rows % PROGRESS_LOG_INTERVAL == 0:
            elapsed = time.time() - self.start_time
            rows_per_sec = self.total_rows / elapsed if elapsed > 0 else 0
            log.info(
                f"Progress: {self.total_rows} rows in {elapsed:.2f}s ({rows_per_sec:.1f} rows/sec)"
            )

    def _process_horizontal_facts_chunk(self, chunk: List[Dict[str, Any]]):
        """Process chunk for horizontal facts format."""
        if not self.fact_col or not self.value_col:
            # Identify fact and value columns from first chunk
            self.fact_col, self.value_col = identify_fact_value_columns(chunk[0])
            if not self.fact_col or not self.value_col:
                log.warning(
                    "Could not identify fact/value columns, falling back to standard format"
                )
                self.horizontal_facts = False
                self.write_data_chunk(chunk)
                return

            self.index_cols = identify_index_columns(
                chunk[0], self.fact_col, self.value_col
            )
            log.info(
                f"Horizontal facts: fact='{self.fact_col}', value='{self.value_col}', "
                f"index_cols={len(self.index_cols)}"
            )

        # For large datasets, use temporary file storage
        if self.total_rows > MEDIUM_DATASET_THRESHOLD and not self.temp_data_file:
            self._create_temp_data_file()

        # Collect facts and values
        for row in chunk:
            fact = row.get(self.fact_col)
            if fact is not None:
                self.unique_facts.add(fact)
                index_key = tuple(row.get(col) for col in self.index_cols)

                if index_key not in self.fact_values:
                    self.fact_values[index_key] = {}

                self.fact_values[index_key][fact] = row.get(self.value_col)

        self.total_rows += len(chunk)

    def _create_temp_data_file(self):
        """Create temporary file for storing intermediate data."""
        self.temp_data_file = tempfile.NamedTemporaryFile(
            mode="w+", delete=False, suffix=".tmp"
        )
        self.temp_files.append(self.temp_data_file.name)
        log.info(f"Created temporary data file: {self.temp_data_file.name}")

        # On Windows, ensure the file handle is properly managed
        if IS_WINDOWS:
            # Flush any pending writes
            self.temp_data_file.flush()
            os.fsync(self.temp_data_file.fileno())

    def finalize_horizontal_facts(self):
        """Finalize horizontal facts format and write to Excel."""
        if not self.horizontal_facts or not self.fact_col or not self.value_col:
            return

        log.info(
            f"Finalizing horizontal facts: {len(self.unique_facts)} facts, "
            f"{len(self.fact_values)} index combinations"
        )

        # Create headers: index columns + sorted facts
        sorted_facts = sorted(self.unique_facts)
        self.headers = self.index_cols + sorted_facts
        self.headers_written = True

        # Create sheet and write headers
        self._create_new_sheet()

        # Write data rows
        for index_combo, facts_dict in self.fact_values.items():
            row_values = list(index_combo)  # Start with index values

            # Add fact values
            for fact in sorted_facts:
                row_values.append(clean_excel_value(facts_dict.get(fact, "")))

            # Write row
            for col, value in enumerate(row_values):
                self.current_sheet.write(self.current_sheet_rows, col, value)

            self.current_sheet_rows += 1

        log.info(f"Horizontal facts finalized: {self.current_sheet_rows - 1} data rows")

    def add_info_sheet(self, job_info: Any):
        """Store job info to be added as the last sheet during save."""
        # Store job_info to be processed during save()
        self.job_info = job_info
        log.debug("Job info stored for info sheet creation during save")

    def _create_info_sheet(self):
        """Create the info sheet as the last sheet using the unified generator."""
        if not self.job_info:
            return

        try:
            # Use the unified info sheet generator
            info_generator = InfoSheetGenerator(self.workbook)
            info_generator.create_info_sheet(self.job_info, self.filename)
            log.info("Info sheet created using unified generator")

        except Exception as e:
            log.warning(f"Error adding info sheet: {e}", exc_info=True)

    def save(
        self, progress_callback: Optional[Callable[[str, float], None]] = None
    ) -> str:
        """Save the workbook and return the file path."""
        try:
            if progress_callback:
                progress_callback("saving_init", 0)

            # Finalize horizontal facts if needed
            if self.horizontal_facts and not self.headers_written:
                self.finalize_horizontal_facts()
                if progress_callback:
                    progress_callback("saving_init", 20)

            # Create info sheet as the last sheet
            self._create_info_sheet()
            if progress_callback:
                progress_callback("saving_init", 40)

            if progress_callback:
                progress_callback("saving_file", 50)

            # Close any temporary data files first
            if hasattr(self, "temp_data_file") and self.temp_data_file:
                try:
                    self.temp_data_file.close()
                    log.debug("Closed temporary data file handle")
                except Exception as e:
                    log.warning(f"Error closing temporary data file: {e}")

            # Close workbook (this saves the file in xlsxwriter)
            self.workbook.close()

            # On Windows, add a small delay to ensure file handles are released
            if IS_WINDOWS:
                time.sleep(0.1)

            if progress_callback:
                progress_callback("saving_complete", 100)

            # Log completion
            elapsed = time.time() - self.start_time
            file_size = os.path.getsize(self.temp_file_path)
            final_memory = get_memory_usage_mb()
            memory_used = final_memory - self.initial_memory_mb

            log.info(f"Excel file saved: {file_size / 1024:.1f} KB")
            log.info(
                f"Performance: {self.total_rows} rows in {elapsed:.2f}s "
                f"({self.total_rows / elapsed:.1f} rows/sec)"
            )
            log.info(f"Memory usage: {final_memory:.1f}MB (+{memory_used:.1f}MB)")
            log.info(f"Sheets created: {len(self.sheets)}")

            return self.temp_file_path

        except Exception as e:
            log.error(f"Error saving Excel file: {e}", exc_info=True)
            raise

    def cleanup(self):
        """Clean up temporary files with Windows-specific retry logic."""
        cleanup_success = True

        try:
            # Ensure workbook is closed before cleanup
            if hasattr(self, "workbook") and self.workbook:
                try:
                    self.workbook.close()
                    log.debug("Ensured workbook is closed before cleanup")
                except Exception as e:
                    log.warning(f"Error ensuring workbook closure: {e}")

            # Close any open temporary data file handles
            if hasattr(self, "temp_data_file") and self.temp_data_file:
                try:
                    self.temp_data_file.close()
                    log.debug("Closed temporary data file handle before cleanup")
                except Exception as e:
                    log.warning(f"Error closing temporary data file handle: {e}")

            # On Windows, add a delay to ensure all handles are released
            if IS_WINDOWS:
                time.sleep(0.2)

            # Remove main temp file
            if not safe_remove_file(self.temp_file_path):
                cleanup_success = False

            # Remove additional temp files
            for temp_file in self.temp_files:
                if not safe_remove_file(temp_file):
                    cleanup_success = False

            if cleanup_success:
                log.debug("All temporary files cleaned up successfully")
            else:
                log.warning("Some temporary files could not be cleaned up")

        except Exception as e:
            log.error(f"Error cleaning up temp files: {e}", exc_info=True)


class EnhancedStreamingResponse(StreamingResponse):
    """Enhanced streaming response for Excel files."""

    def __init__(
        self,
        content_generator,
        filename: str,
        background: Optional[BackgroundTask] = None,
    ):
        media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        headers = {"Content-Disposition": f"attachment; filename={filename}.xlsx"}
        headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        headers["Pragma"] = "no-cache"
        headers["Expires"] = "0"

        super().__init__(
            content=content_generator,
            media_type=media_type,
            headers=headers,
            background=background,
        )
