# Changelog

All notable changes to the Magic Gateway project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.4.2] - 2025-05-20

### Changed
- Bumped version number for consistency across all files

## [0.4.1] - 2025-05-12

### Added
- Added Parquet format support for data exports
- Improved metadata handling in export files

## [0.4.0] - 2025-04-26

### Added
- Implemented true streaming Excel export that starts download immediately
- Optimized Excel generation for large datasets
- Improved memory usage during export operations
- Added consistent Arial font styling for all Excel exports
- Fixed display of Panel ID and filters in Excel exports

### Changed
- Replaced in-memory Excel generation with a more efficient streaming approach
- Improved error handling during Excel generation
- Reduced server load during export operations

## [0.3.2] - 2025-04-24

### Added
- New endpoint `/api/v1/postgres/ctlg_measures/` for retrieving catalog measures
- Support for filtering measures by support status

## [0.3.1] - 2025-04-24

### Added
- Export endpoint for job data
- Support for CSV and Excel export formats
- Info sheet in Excel exports with job and result information

## [0.3.0] - 2025-04-09

### Added
- New output format "queries_split" for PostgreSQL to ClickHouse conversion
- Improved scripts module structure

### Changed
- Moved PostgreSQL to ClickHouse conversion functionality to scripts module
- Updated client structure to better reflect server API

## [0.2.2] - 2025-04-08

### Added
- New endpoint `/api/v1/postgres/axis-info` for retrieving axis information and labels
- Support for full axis names with schema (format: schema.axis_name)
- Filtering by clientdb_schema in axis queries

### Changed
- Improved error messages for axis info endpoint

## [0.2.1] - 2025-04-07

### Added
- Support for PostgreSQL to ClickHouse conversion
- Health check endpoint
- Request tracking middleware

### Fixed
- Connection pooling issues
- Authentication token refresh mechanism

## [0.2.0] - 2025-04-01

### Added
- Initial release with basic functionality
- Support for ClickHouse and PostgreSQL queries
- LDAP authentication
- Request tracking
