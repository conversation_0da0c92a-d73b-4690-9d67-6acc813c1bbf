# magic_gateway/core/lifespan.py

from contextlib import asynccontextmanager
from typing import Async<PERSON>enerator
import asyncio  # Import asyncio

from fastapi import FastAPI

from magic_gateway.core.config import settings
from magic_gateway.core.logging_config import log
from magic_gateway.db.connection_manager import (
    clickhouse_connection_manager,
    postgres_connection_manager,
    logs_connection_manager,
)

# Removed PostgresHandler import as it's no longer needed
from magic_gateway.tracking.service import RequestTrackingService  # Import service
from magic_gateway.api.middleware import set_request_tracker_instance  # Import setter
from magic_gateway.monitoring.service import (
    connection_pool_monitoring_service,
)  # Import monitoring service


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan context manager.
    """
    log.info("Application starting up...")

    # Initialize Request Tracking Service
    # You could potentially pass dependencies like the DB handler if needed,
    # but here it uses the static methods of PostgresHandler directly.
    request_tracker = RequestTrackingService()
    app.state.request_tracker_service = request_tracker  # Store in app state
    set_request_tracker_instance(request_tracker)  # Set for middleware global access
    log.info("Request Tracking Service initialized.")

    # Initialize Database Connections
    postgres_initialized = False
    clickhouse_initialized = False
    logs_initialized = False
    postgres_init_task = None
    clickhouse_init_task = None
    logs_init_task = None

    # Start initializations concurrently
    try:
        log.info("Initializing database connection pools concurrently...")
        postgres_init_task = asyncio.create_task(
            postgres_connection_manager.initialize()
        )
        clickhouse_init_task = asyncio.create_task(
            clickhouse_connection_manager.initialize()
        )
        logs_init_task = asyncio.create_task(logs_connection_manager.initialize())

        # Wait for PostgreSQL initialization
        try:
            await postgres_init_task
            postgres_initialized = True
            log.info("PostgreSQL connection pool initialization successful.")
            # Removed automatic table creation for PostgreSQL metadata tables
        except Exception as pg_e:
            log.error(f"Failed to initialize PostgreSQL pool during startup: {pg_e}")
            postgres_initialized = False  # Handled by the exception catch

        # Wait for ClickHouse initialization
        try:
            await clickhouse_init_task
            clickhouse_initialized = True
            log.info("ClickHouse connection pool initialization successful.")
        except Exception as ch_e:
            log.error(f"Failed to initialize ClickHouse pool during startup: {ch_e}")
            clickhouse_initialized = False

        # Wait for Logs database initialization
        try:
            await logs_init_task
            logs_initialized = True
            log.info("Logs database connection initialization successful.")
        except Exception as logs_e:
            log.critical(
                f"Failed to initialize Logs database pool during startup: {logs_e}. Request tracking will be unavailable."
            )
            logs_initialized = False  # Handled by the exception catch

    except Exception as init_e:
        # Catch potential errors during task creation itself
        log.critical(f"Error during concurrent DB initialization setup: {init_e}")

    # Report status
    if not postgres_initialized and not clickhouse_initialized and not logs_initialized:
        log.critical("CRITICAL: All database connections failed to initialize.")
    else:
        if not postgres_initialized:
            log.warning(
                "WARNING: PostgreSQL failed to initialize. Some functionality may be unavailable."
            )
        if not clickhouse_initialized:
            log.warning(
                "WARNING: ClickHouse failed to initialize. ClickHouse queries will be unavailable."
            )
        if not logs_initialized:
            log.warning(
                "WARNING: Logs database failed to initialize. Request tracking and history will be unavailable."
            )
        if postgres_initialized and clickhouse_initialized and logs_initialized:
            log.info("All configured database connections initialized successfully.")

    # Store availability state if needed
    app.state.postgres_available = postgres_initialized
    app.state.clickhouse_available = clickhouse_initialized
    app.state.logs_available = logs_initialized

    # Initialize connection pool monitoring service if enabled
    if settings.ENABLE_CONNECTION_POOL_MONITORING:
        try:
            log.info("Initializing connection pool monitoring service...")
            # Start the monitoring service with the configured collection interval
            await connection_pool_monitoring_service.start(
                collection_interval=settings.CONNECTION_POOL_MONITORING_INTERVAL
            )
            log.info(
                f"Connection pool monitoring service started successfully with {settings.CONNECTION_POOL_MONITORING_INTERVAL}s interval."
            )
            app.state.monitoring_service_available = True
        except Exception as mon_e:
            log.error(
                f"Failed to initialize connection pool monitoring service: {mon_e}"
            )
            app.state.monitoring_service_available = False
    else:
        log.info("Connection pool monitoring is disabled in settings.")
        app.state.monitoring_service_available = False

    log.info("Application startup sequence complete.")
    yield
    log.info("Application shutting down...")

    # Close pools concurrently
    close_tasks = []
    if clickhouse_connection_manager.initialized:
        close_tasks.append(asyncio.create_task(clickhouse_connection_manager.close()))
    else:
        log.info("ClickHouse pool was not initialized, skipping close.")

    if postgres_connection_manager.initialized:
        close_tasks.append(asyncio.create_task(postgres_connection_manager.close()))
    else:
        log.info("PostgreSQL pool was not initialized, skipping close.")

    if logs_connection_manager.initialized:
        close_tasks.append(asyncio.create_task(logs_connection_manager.close()))
    else:
        log.info("Logs database pool was not initialized, skipping close.")

    if close_tasks:
        try:
            await asyncio.gather(*close_tasks)
            log.info("Database connection pools closed.")
        except Exception as close_e:
            log.error(
                f"Error closing database pools during shutdown: {close_e}",
                exc_info=True,
            )

    # Stop the monitoring service
    if (
        hasattr(app.state, "monitoring_service_available")
        and app.state.monitoring_service_available
    ):
        try:
            log.info("Stopping connection pool monitoring service...")
            await connection_pool_monitoring_service.stop()
            log.info("Connection pool monitoring service stopped.")
        except Exception as mon_e:
            log.error(f"Error stopping monitoring service: {mon_e}", exc_info=True)

    log.info("Application shutdown sequence complete.")
