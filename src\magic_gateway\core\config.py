"""Configuration settings for the MagicGateway application."""

from typing import Dict, List, Optional, Union

from pydantic import AnyHttpUrl, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""

    # Application settings
    APP_NAME: str = "MagicGateway"
    APP_VERSION: str = "0.4.3"
    DEBUG: bool = False
    ENVIRONMENT: str = "development"
    LOG_LEVEL: str = "INFO"

    # Server settings
    HOST: str = "0.0.0.0"
    PORT: int = 8000

    # Security settings
    SECRET_KEY: str
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # CORS settings
    BACKEND_CORS_ORIGINS: Union[List[AnyHttpUrl], List[str]] = ["*"]

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        """Parse CORS origins from string to list."""
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # LDAP settings
    # New LDAP settings format
    LDAP_SERVER: str = ""  # LDAP server address
    LDAP_DOMAIN: str = ""  # LDAP domain (NetBIOS name)
    LDAP_DOMAIN_FQDN: str = ""  # LDAP domain FQDN
    LDAP_BASE_DN: str = ""  # LDAP base DN

    # Legacy LDAP settings (for backward compatibility)
    LDAP_SERVER_URL: str = ""  # Will be constructed from LDAP_SERVER
    LDAP_BIND_DN: str = ""  # Not used in new authentication method
    LDAP_BIND_PASSWORD: str = ""  # Not used in new authentication method
    LDAP_USER_SEARCH_BASE: str = ""  # Will use LDAP_BASE_DN if not set
    LDAP_USER_SEARCH_FILTER: str = (
        "(sAMAccountName={username})"  # Default search filter
    )
    LDAP_ADMIN_GROUP_DN: str = ""  # Admin group DN

    # Test user credentials
    TEST_LDAP_USERNAME: str = ""  # Test LDAP username
    TEST_LDAP_PASSWORD: str = ""  # Test LDAP password

    # PostgreSQL settings
    POSTGRES_HOST: str
    POSTGRES_PORT: int
    POSTGRES_USER: str
    POSTGRES_PASSWORD: str
    POSTGRES_DB: str
    POSTGRES_MIN_CONNECTIONS: int = 1
    POSTGRES_MAX_CONNECTIONS: int = 10
    POSTGRES_POOL_TIMEOUT: int = 30  # Timeout for acquiring a connection from the pool
    POSTGRES_MAX_IDLE_TIME: int = 600  # Maximum time a connection can be idle
    POSTGRES_MAX_LIFETIME: int = 3600  # Maximum lifetime of a connection
    POSTGRES_APPLICATION_NAME: str = (
        "magic_gateway"  # Application name for identification in PostgreSQL logs
    )

    @property
    def POSTGRES_DSN(self) -> str:
        """Construct PostgreSQL DSN from settings."""
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}?application_name={self.POSTGRES_APPLICATION_NAME}"

    # Logs Database settings (separate PostgreSQL connection for logs)
    LOGS_DB_HOST: str
    LOGS_DB_PORT: int
    LOGS_DB_USER: str
    LOGS_DB_PASSWORD: str
    LOGS_DB_NAME: str
    LOGS_DB_SCHEMA: str

    @property
    def LOGS_DB_DSN(self) -> str:
        """Construct Logs Database DSN from settings."""
        return f"postgresql://{self.LOGS_DB_USER}:{self.LOGS_DB_PASSWORD}@{self.LOGS_DB_HOST}:{self.LOGS_DB_PORT}/{self.LOGS_DB_NAME}?application_name={self.POSTGRES_APPLICATION_NAME}_logs"

    # ClickHouse settings
    CLICKHOUSE_HOST: str
    CLICKHOUSE_PORT: int  # Native client port (usually 9000)
    CLICKHOUSE_HTTP_PORT: int = 8123  # HTTP interface port (usually 8123)
    CLICKHOUSE_USER: str
    CLICKHOUSE_PASSWORD: str
    CLICKHOUSE_DATABASE: str
    CLICKHOUSE_MIN_CONNECTIONS: int = 3
    CLICKHOUSE_MAX_CONNECTIONS: int = 10
    CLICKHOUSE_CONNECT_TIMEOUT: int = 10  # Connection timeout in seconds
    CLICKHOUSE_PING_TIMEOUT: int = 5  # Timeout for liveness check in seconds
    CLICKHOUSE_POOL_WAIT_TIMEOUT: int = (
        30  # Timeout for waiting for a connection from the pool
    )
    CLICKHOUSE_SOCKET_TIMEOUT: int = 30  # Socket timeout for send/receive operations

    # Query settings
    MAX_QUERY_EXECUTION_TIME: int = 300  # seconds
    TRACK_QUERIES: bool = True

    # Monitoring settings
    ENABLE_CONNECTION_POOL_MONITORING: bool = True
    CONNECTION_POOL_MONITORING_INTERVAL: int = 60  # seconds
    CONNECTION_POOL_MONITORING_RETENTION_DAYS: int = 7  # days to keep monitoring data

    # Session settings
    SESSION_TIMEOUT: int = 1800  # 30 minutes in seconds

    # Table mappings for PostgreSQL to ClickHouse conversion
    # This dictionary maps PostgreSQL table names to ClickHouse table names
    # Format: {"pg_schema.pg_table": "ch_database.ch_table"}
    TABLES_MAPPING: Dict[str, str] = {
        "movements_pet": "pet.purchases_1",  # Base mapping without id_panel
        "ctlg_shops_pet": "pet.ctlg_shops",
        "cps.ctlg_shops_pet": "pet.ctlg_shops",
        "household_pet": "pet.household",
        "cps.household_pet": "pet.household",
        "art_product_groups_pet": "pet.ctlg_article_plus",
        "cps.art_product_groups_pet": "pet.ctlg_article_plus",
    }

    def get_table_mapping(self, id_panel: Optional[str] = None) -> Dict[str, str]:
        """
        Get table mapping dictionary, optionally with id_panel applied.

        Args:
            id_panel: Optional ID panel to append to certain table names

        Returns:
            Dictionary mapping PostgreSQL table names to ClickHouse table names
        """
        # Create a copy of the mapping to avoid modifying the original
        mapping = self.TABLES_MAPPING.copy()

        # Apply id_panel to specific tables if provided
        if id_panel:
            # Update specific mappings that need id_panel
            for pg_table, ch_table in self.TABLES_MAPPING.items():
                if pg_table == "movements_pet":
                    mapping[pg_table] = f"{ch_table[:-1]}{id_panel}"

        return mapping

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=True
    )


# Create a global settings instance
settings = Settings()
