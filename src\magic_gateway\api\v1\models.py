"""API models for the MagicGateway application."""

import uuid
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List, Union, Literal

from pydantic import BaseModel, Field


# Authentication models
class Token(BaseModel):
    """Model for an authentication token."""

    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class TokenPayload(BaseModel):
    """Model for a token payload."""

    sub: Optional[str] = None
    exp: Optional[int] = None
    iat: Optional[int] = None
    type: Optional[str] = None
    user_id: Optional[int] = None
    is_admin: Optional[bool] = None
    auth_source: Optional[str] = None


class User(BaseModel):
    """Model for a user."""

    username: str
    is_admin: bool = False
    auth_source: str = "ldap"


# Script models
class ScriptParameter(BaseModel):
    """Model for a script parameter."""

    name: str
    type: str
    required: bool
    default: Optional[Any] = None
    description: Optional[str] = None


class ScriptMetadata(BaseModel):
    """Model for script metadata."""

    name: str
    description: str
    author: Optional[str] = None
    version: Optional[str] = None
    requires_admin: bool = False


class ScriptInfo(BaseModel):
    """Model for script information."""

    name: str
    metadata: ScriptMetadata
    parameters: List[ScriptParameter]


class ScriptRequest(BaseModel):
    """Model for a script execution request."""

    parameters: Dict[str, Any] = {}
    script_id: Optional[str] = None


class ScriptResponse(BaseModel):
    """Model for a script execution response."""

    script_id: str
    script_name: str
    start_time: datetime
    end_time: datetime
    execution_time: float
    result: Any
    error: Optional[str] = None


# Health check models
class DatabaseStatus(BaseModel):
    """Model for database connection status."""

    connected: bool
    message: Optional[str] = None
    last_checked: datetime


class ConnectionPoolStatus(BaseModel):
    """Model for connection pool status."""

    pool_type: str
    active_connections: int
    idle_connections: int
    min_size: int
    max_size: int
    utilization_percent: float
    status: str  # 'healthy', 'warning', 'critical'
    last_updated: datetime
    acquisition_time_ms: Optional[float] = None
    usage_time_ms: Optional[float] = None
    timeouts: Optional[int] = None
    errors: Optional[int] = None


class HealthCheck(BaseModel):
    """Model for a health check response."""

    status: str
    version: str
    timestamp: datetime
    databases: Dict[str, DatabaseStatus]
    connection_pools: Dict[str, ConnectionPoolStatus]
    uptime: float


# Error models
class ErrorResponse(BaseModel):
    """Model for an error response."""

    detail: str


# PostgreSQL to ClickHouse conversion models
class PgToClickHouseConversionRequest(BaseModel):
    """Model for a PostgreSQL to ClickHouse conversion request."""

    object_name: str = Field(
        ..., description="Full object name including schema (e.g., 'schema.table_name')"
    )
    output_mode: Optional[str] = Field(
        "combined",
        description="Output mode: 'normal' for a single statement, 'combined' for WITH clause, 'cte_only' for CTE part, 'queries_only' for queries part, 'queries_split' for list of queries split by UNION ALL, 'click_app' for list with CTE and queries as dictionary",
    )
    id_panel: Optional[str] = Field(
        None,
        description="Panel ID to use for table mapping (e.g., '5' or '8'). If not provided, will be used main panel if applicable.",
    )
    timeout_seconds: Optional[int] = Field(
        None,
        description="Custom timeout in seconds for this request. If not provided, the default MAX_QUERY_EXECUTION_TIME will be used.",
    )


class PgToClickHouseConversionResponse(BaseModel):
    """Model for a PostgreSQL to ClickHouse conversion response."""

    request_id: uuid.UUID
    original_ddl: str
    clickhouse_sql: Union[str, List[str], Dict[str, Any]] = Field(
        ...,
        description="ClickHouse SQL result. String for most output modes, list of strings for 'queries_split' mode, dictionary for 'click_app' mode",
    )
    output_mode: str
    status: str = "completed"


# PostgreSQL to ClickHouse view checker models
class PgToChViewCheckerRequest(BaseModel):
    """Model for a PostgreSQL to ClickHouse view checker request."""

    pg_view_name: str = Field(
        ...,
        description="Full name of PostgreSQL view including schema (e.g., 'schema.view_name')",
    )
    id_panel: Optional[str] = Field(
        None,
        description="Panel ID to use for table mapping (e.g., '5' or '8'). If not provided, will be used main panel.",
    )

    timeout_seconds: Optional[int] = Field(
        None,
        description="Custom timeout in seconds for this request. If not provided, the default MAX_QUERY_EXECUTION_TIME will be used.",
    )


class PgToChViewCheckerResponse(BaseModel):
    """Model for a PostgreSQL to ClickHouse view checker response."""

    request_id: uuid.UUID
    pg_view_name: str
    pg_row_count: int
    ch_row_count: int
    id_panel: int
    row_count_match: bool
    status: str
    error: Optional[str] = None


# Data export models (for scripts endpoint)
class ExportFormat(str, Enum):
    """Enum for export formats."""

    CSV = "csv"
    EXCEL = "excel"
    EXCEL_FACTS = "excel_facts"
    PARQUET = "parquet"
